body {
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

.sidebar {
    width: 220px;
    background-color: #343a40;
    color: #fff;
    position: fixed;
    height: 100vh;
    padding-top: 20px;
}

.sidebar h2 {
    text-align: center;
    margin-bottom: 30px;
}

.sidebar a {
    display: block;
    color: #fff;
    padding: 15px 20px;
    text-decoration: none;
}

.sidebar a:hover {
    background-color: #495057;
}

.main-content {
    margin-left: 220px;
    padding: 20px;
}

.card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card h3 {
    margin: 0 0 10px;
}

.menu-toggle {
    display: none;
    position: absolute;
    top: 15px;
    left: 15px;
    background: #343a40;
    color: white;
    border: none;
    font-size: 24px;
    padding: 5px 10px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .sidebar {
        position: absolute;
        width: 200px;
        height: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .menu-toggle {
        display: block;
    }
}
