<?php
session_start();

// Sekat akses jika belum login (aktifkan kemudian jika login sudah siap)
// if (!isset($_SESSION['admin_user'])) {
//     header('Location: admin.php');
//     exit;
// }
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="admin-style.css">
</head>
<body>
    <button class="menu-toggle" onclick="toggleMenu()">☰</button>
    <div class="sidebar">
        <h2>Admin Panel</h2>
        <a href="#">Dashboard</a>
        <a href="#">Menu</a>
        <a href="#">Tempahan</a>
        <a href="#">Pengguna</a>
        <a href="#"><PERSON>g <PERSON></a>
    </div>
    <div class="main-content">
        <h1>Selamat Datang ke Dashboard Admin</h1>
        <div class="card">
            <h3>Statistik Harian</h3>
            <p>Contoh: 20 tempahan baru hari ini</p>
        </div>
        <div class="card">
            <h3>Kemaskini Menu</h3>
            <p>Pautan atau butang untuk kemaskini menu restoran.</p>
        </div>
        <div class="card">
            <h3>Urus Pengguna</h3>
            <p>Senarai pengguna, tambah atau padam admin, dll.</p>
        </div>
    </div>
    <script>
        function toggleMenu() {
            document.querySelector('.sidebar').classList.toggle('active');
        }
    </script>
</body>
</html>
